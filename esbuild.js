const esbuild = require("esbuild");

const production = process.argv.includes('--production');
const watch = process.argv.includes('--watch');

/**
 * @type {import('esbuild').Plugin}
 */
const esbuildProblemMatcherPlugin = {
	name: 'esbuild-problem-matcher',

	setup(build) {
		build.onStart(() => {
			console.log('[watch] build started');
		});
		build.onEnd((result) => {
			result.errors.forEach(({ text, location }) => {
				console.error(`✘ [ERROR] ${text}`);
				console.error(`    ${location.file}:${location.line}:${location.column}:`);
			});
			console.log('[watch] build finished');
		});
	},
};

async function main() {
	// 构建扩展主文件
	const extensionCtx = await esbuild.context({
		entryPoints: [
			'src/extension.ts'
		],
		bundle: true,
		format: 'cjs',
		minify: production,
		sourcemap: !production,
		sourcesContent: false,
		platform: 'node',
		outfile: 'dist/extension.js',
		external: ['vscode'],
		logLevel: 'silent',
		plugins: [
			esbuildProblemMatcherPlugin,
		],
	});

	// 构建webview React应用
	const webviewCtx = await esbuild.context({
		entryPoints: [
			'src/webview/index.tsx'
		],
		bundle: true,
		format: 'iife',
		minify: production,
		sourcemap: !production,
		sourcesContent: false,
		platform: 'browser',
		outfile: 'dist/webview.js',
		logLevel: 'silent',
		jsx: 'automatic',
		plugins: [
			esbuildProblemMatcherPlugin,
		],
	});

	// 只在生产模式下构建 Mastra 应用（开发时使用 mastra dev）
	let mastraCtx = null;
	if (production) {
		console.log('[mastra] 生产模式：构建 Mastra 应用...');
		mastraCtx = await esbuild.context({
			entryPoints: [
				'src/mastra/index.ts'
			],
			bundle: true,
			format: 'esm',
			minify: production,
			sourcemap: !production,
			sourcesContent: false,
			platform: 'node',
			outfile: 'dist/mastra.mjs',
			external: [],
			logLevel: 'silent',
			plugins: [
				esbuildProblemMatcherPlugin,
			],
			target: 'node22',
			// 确保 ES 模块格式正确
			banner: {
				js: '// Mastra application bundle\n'
			}
		});
	} else {
		console.log('[mastra] 开发模式：跳过 Mastra 构建（请使用 mastra dev 启动服务）');
	}

	const contexts = [extensionCtx, webviewCtx];
	if (mastraCtx) {
		contexts.push(mastraCtx);
	}

	if (watch) {
		await Promise.all(contexts.map(ctx => ctx.watch()));
	} else {
		await Promise.all(contexts.map(ctx => ctx.rebuild()));
		await Promise.all(contexts.map(ctx => ctx.dispose()));
	}
}

main().catch(e => {
	console.error(e);
	process.exit(1);
});
