const esbuild = require("esbuild");

const production = process.argv.includes('--production');
const watch = process.argv.includes('--watch');

/**
 * @type {import('esbuild').Plugin}
 */
const esbuildProblemMatcherPlugin = {
	name: 'esbuild-problem-matcher',

	setup(build) {
		build.onStart(() => {
			console.log('[watch] build started');
		});
		build.onEnd((result) => {
			result.errors.forEach(({ text, location }) => {
				console.error(`✘ [ERROR] ${text}`);
				console.error(`    ${location.file}:${location.line}:${location.column}:`);
			});
			console.log('[watch] build finished');
		});
	},
};

async function main() {
	// 构建扩展主文件
	const extensionCtx = await esbuild.context({
		entryPoints: [
			'src/extension.ts'
		],
		bundle: true,
		format: 'cjs',
		minify: production,
		sourcemap: !production,
		sourcesContent: false,
		platform: 'node',
		outfile: 'dist/extension.js',
		external: ['vscode'],
		logLevel: 'silent',
		plugins: [
			esbuildProblemMatcherPlugin,
		],
	});

	// 构建webview React应用
	const webviewCtx = await esbuild.context({
		entryPoints: [
			'src/webview/index.tsx'
		],
		bundle: true,
		format: 'iife',
		minify: production,
		sourcemap: !production,
		sourcesContent: false,
		platform: 'browser',
		outfile: 'dist/webview.js',
		logLevel: 'silent',
		jsx: 'automatic',
		plugins: [
			esbuildProblemMatcherPlugin,
		],
	});

	// 构建 Mastra 应用
	const mastraCtx = await esbuild.context({
		entryPoints: [
			'src/mastra/index.ts'
		],
		bundle: true,
		format: 'esm',
		minify: production,
		sourcemap: !production,
		sourcesContent: false,
		platform: 'node',
		outfile: 'dist/mastra.mjs',
		external: [
			// 排除 Node.js 内置模块
			'fs', 'path', 'crypto', 'http', 'https', 'url', 'stream', 'util', 'events', 'buffer',
			// 排除可能的大型依赖，让它们在运行时解析
			'@mastra/core', '@mastra/libsql', '@ai-sdk/openai', 'ai', 'zod'
		],
		logLevel: 'silent',
		plugins: [
			esbuildProblemMatcherPlugin,
		],
		target: 'node18',
		// 确保 ES 模块格式正确
		banner: {
			js: '// Mastra application bundle\n'
		}
	});

	if (watch) {
		await Promise.all([
			extensionCtx.watch(),
			webviewCtx.watch(),
			mastraCtx.watch()
		]);
	} else {
		await Promise.all([
			extensionCtx.rebuild(),
			webviewCtx.rebuild(),
			mastraCtx.rebuild()
		]);
		await Promise.all([
			extensionCtx.dispose(),
			webviewCtx.dispose(),
			mastraCtx.dispose()
		]);
	}
}

main().catch(e => {
	console.error(e);
	process.exit(1);
});
