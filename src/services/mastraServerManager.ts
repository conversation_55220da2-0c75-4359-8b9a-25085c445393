import * as vscode from 'vscode';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';

export class MastraServerManager {
    private mastraProcess: ChildProcess | null = null;
    private context: vscode.ExtensionContext;
    private port: number = 4111;
    private isProduction: boolean;
    private useBuildOutput: boolean;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;

        // 从配置中获取设置
        const config = vscode.workspace.getConfiguration('aicode');
        this.port = config.get('mastraPort', 4111);
        this.useBuildOutput = config.get('useBuildOutput', true);

        // 判断是否为生产环境（打包后的插件或强制生产模式）
        const forceProductionMode = config.get('useProductionMode', false);
        this.isProduction = context.extensionMode === vscode.ExtensionMode.Production || forceProductionMode;
    }

    async start(): Promise<void> {
        try {
            console.log('启动Mastra服务器...当前环境变量：', !this.isProduction ? '生产' : '开发');
            if (this.isProduction) {
                await this.startProductionServer();

            } else {
                await this.startDevelopmentServer();
            }

            // 等待服务器启动
            await this.waitForServer();
            vscode.window.showInformationMessage(`Mastra 服务器已启动在端口 ${this.port}`);
        } catch (error) {
            vscode.window.showErrorMessage(`启动 Mastra 服务器失败: ${error}`);
        }
    }

    private async startDevelopmentServer(): Promise<void> {
        console.log('启动开发服务器...');
        const workspaceRoot = this.getWorkspaceRoot();

        // 检查是否启用构建产物模式且构建产物存在
        if (this.useBuildOutput) {
            const mastraOutputPath = path.join(workspaceRoot, '.mastra', 'output');
            const fs = require('fs');

            if (fs.existsSync(mastraOutputPath)) {
                console.log('使用构建产物启动开发服务器');
                await this.startFromBuildOutput(mastraOutputPath);
                return;
            } else {
                console.log('构建产物不存在，回退到包管理器模式');
            }
        }

        // 使用包管理器启动开发服务器
        await this.startDevelopmentWithPackageManager(workspaceRoot);
    }

    private async startDevelopmentWithPackageManager(workspaceRoot: string): Promise<void> {
        console.log('使用包管理器启动开发服务器...');
        // 检查是否有 pnpm
        const packageManager = await this.detectPackageManager(workspaceRoot);
        // 使用检测到的包管理器启动开发服务器
        this.mastraProcess = spawn(packageManager, ['run', 'dev', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            }
        });

        this.setupProcessHandlers('开发(包管理器)');
    }

    private async startProductionServer(): Promise<void> {
        const workspaceRoot = this.getWorkspaceRoot();
        console.log('启动生产环境服务...');

        // 检查是否启用构建产物模式且构建产物存在
        if (this.useBuildOutput) {
            const mastraOutputPath = path.join(workspaceRoot, '.mastra', 'output');
            const fs = require('fs');

            if (fs.existsSync(mastraOutputPath)) {
                console.log('使用构建产物启动生产服务器');
                await this.startFromBuildOutput(mastraOutputPath);
                return;
            } else {
                console.log('构建产物不存在，回退到包管理器模式');
            }
        }

        // 使用包管理器启动
        await this.startWithPackageManager(workspaceRoot);
    }

    private async startFromBuildOutput(outputPath: string): Promise<void> {
        console.log('使用构建产物启动Mastra服务器...');
        const fs = require('fs');

        // 检查必要文件是否存在
        const indexPath = path.join(outputPath, 'index.mjs');
        const packageJsonPath = path.join(outputPath, 'package.json');

        if (!fs.existsSync(indexPath)) {
            throw new Error('构建产物中缺少 index.mjs 文件');
        }

        if (!fs.existsSync(packageJsonPath)) {
            throw new Error('构建产物中缺少 package.json 文件');
        }

        // 首先安装依赖（如果需要）
        await this.installBuildDependencies(outputPath);

        // 启动服务器
        this.mastraProcess = spawn('node', [
            '--import=./instrumentation.mjs',
            '--import=@opentelemetry/instrumentation/hook.mjs',
            './index.mjs'
        ], {
            cwd: outputPath,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: false,
            env: {
                ...process.env,
                NODE_ENV: 'production',
                PORT: this.port.toString()
            }
        });

        console.log(`从构建产物启动生产服务器，端口：${this.port}`);
        this.setupProcessHandlers('生产(构建产物)');
    }

    private async startWithPackageManager(workspaceRoot: string): Promise<void> {
        console.log('使用包管理器启动生产服务器...');
        // 检测包管理器
        const packageManager = await this.detectPackageManager(workspaceRoot);

        // 首先构建项目
        // await this.buildProject(packageManager);

        // 然后启动生产服务器
        this.mastraProcess = spawn(packageManager, ['run', 'start', '--port', this.port.toString()], {
            cwd: workspaceRoot,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true,
            env: {
                ...process.env,
                NODE_ENV: 'production'
            }
        });
        console.log(`使用包管理器启动生产服务器，端口：${this.port}`);
        this.setupProcessHandlers('生产(包管理器)');
    }

    private async installBuildDependencies(outputPath: string): Promise<void> {
        const fs = require('fs');
        const nodeModulesPath = path.join(outputPath, 'node_modules');

        // 如果 node_modules 已存在，跳过安装
        if (fs.existsSync(nodeModulesPath)) {
            console.log('构建产物依赖已存在，跳过安装');
            return;
        }

        console.log('安装构建产物依赖...');

        return new Promise((resolve, reject) => {
            // 检测包管理器
            const packageManager = fs.existsSync(path.join(outputPath, 'pnpm-lock.yaml')) ? 'pnpm' : 'npm';

            const installProcess = spawn(packageManager, ['install'], {
                cwd: outputPath,
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            installProcess.stdout?.on('data', (data) => {
                console.log(`依赖安装输出: ${data}`);
            });

            installProcess.stderr?.on('data', (data) => {
                console.error(`依赖安装错误: ${data}`);
            });

            installProcess.on('close', (code) => {
                if (code === 0) {
                    console.log('构建产物依赖安装完成');
                    resolve();
                } else {
                    reject(new Error(`依赖安装失败，退出码: ${code}`));
                }
            });

            installProcess.on('error', (error) => {
                reject(error);
            });
        });
    }

    private async buildProject(packageManager: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const workspaceRoot = this.getWorkspaceRoot();

            const buildProcess = spawn(packageManager, ['run', 'build'], {
                cwd: workspaceRoot,
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            buildProcess.stdout?.on('data', (data) => {
                // Build output
            });

            buildProcess.stderr?.on('data', (data) => {
                // Build error output
            });

            buildProcess.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`构建失败，退出码: ${code}`));
                }
            });

            buildProcess.on('error', (error) => {
                reject(error);
            });
        });
    }

    private async detectPackageManager(workspaceRoot: string): Promise<string> {
        const fs = require('fs');

        // 检查 pnpm-lock.yaml
        if (fs.existsSync(path.join(workspaceRoot, 'pnpm-lock.yaml'))) {
            return 'pnpm';
        }

        // 检查 yarn.lock
        if (fs.existsSync(path.join(workspaceRoot, 'yarn.lock'))) {
            return 'yarn';
        }

        // 检查 package-lock.json
        if (fs.existsSync(path.join(workspaceRoot, 'package-lock.json'))) {
            return 'npm';
        }

        // 默认使用 npm
        return 'npm';
    }

    private setupProcessHandlers(mode: string): void {
        if (!this.mastraProcess) return;

        this.mastraProcess.stdout?.on('data', (data) => {
            // Process output
        });

        this.mastraProcess.stderr?.on('data', (data) => {
            // Process error output
        });

        this.mastraProcess.on('close', (code) => {
            this.mastraProcess = null;
        });

        this.mastraProcess.on('error', (error) => {
            vscode.window.showErrorMessage(`Mastra ${mode}服务器错误: ${error.message}`);
        });
    }

    private async waitForServer(): Promise<void> {
        const maxAttempts = 30;
        const delay = 10000;
        for (let i = 0; i < maxAttempts; i++) {
            try {
                // 健康检查：尝试连接服务器
                const response = await fetch(`http://localhost:${this.port}`, {
                    method: 'GET',
                    signal: AbortSignal.timeout(3000)
                });

                if (response.ok) {
                    return;
                }
            } catch (error) {
                // 服务器还未准备好，继续等待
            }

            await new Promise(resolve => setTimeout(resolve, delay));
        }

        throw new Error('Mastra 服务器启动超时，请检查日志');
    }

    private getWorkspaceRoot(): string {

        // 获取插件所在的工作区根目录
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            return workspaceFolders[0].uri.fsPath;
        }
        // 如果没有工作区，使用插件目录
        return this.context.extensionPath;
    }

    stop(): void {
        if (this.mastraProcess) {
            this.mastraProcess.kill('SIGTERM');
            this.mastraProcess = null;
        }
    }

    getServerUrl(): string {
        return `http://localhost:${this.port}`;
    }

    getApiUrl(): string {
        return `${this.getServerUrl()}/api`;
    }

    isRunning(): boolean {
        return this.mastraProcess !== null && !this.mastraProcess.killed;
    }
}
